{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Reception Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-tie mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'add_reception' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus mr-1"></i> Add New Reception
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'manage_reception' %}">
                                        <i class="fas fa-list mr-2"></i>All Receptions
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_reception' %}?is_active=True">
                                        <i class="fas fa-user-check mr-2"></i>Active Receptions
                                    </a>
                                    <a class="dropdown-item" href="{% url 'manage_reception' %}?is_active=False">
                                        <i class="fas fa-user-times mr-2"></i>Inactive Receptions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Full Name</th>
                                        <th>Email</th>
                                        <th>Gender</th>
                                        <th style="width: 80px;">Avatar</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reception in allReception %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">

                                                    <strong>{{ reception.first_name }} {{ reception.last_name }}</strong>
                                                    {% if reception.is_active %}
                                                    <span class="badge badge-success badge-sm ml-1">Active</span>
                                                    {% else %}
                                                    <span class="badge badge-danger badge-sm ml-1">Inactive</span>
                                                    {% endif %}

                                        </td>
                                        <td>
                                            <a href="mailto:{{ reception.email }}" class="text-primary">
                                                <i class="fas fa-envelope mr-1"></i>{{ reception.email }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if reception.gender == 'Male' %}
                                            <span class="badge badge-info">
                                                <i class="fas fa-mars mr-1"></i>{{ reception.gender }}
                                            </span>
                                            {% else %}
                                            <span class="badge badge-pink">
                                                <i class="fas fa-venus mr-1"></i>{{ reception.gender }}
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if reception.profile_pic %}
                                            <img class="img-circle elevation-2" style="width: 50px; height: 50px;"
                                                src="{{ reception.profile_pic }}" alt="Reception Avatar">
                                            {% else %}
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 50px; height: 50px;">
                                                <i class="fas fa-user-tie text-white"></i>
                                            </div>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'edit_reception' reception.reception.id %}" class="btn btn-info btn-sm" title="Edit Reception">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_reception' reception.reception.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Reception" data-confirm="Are you sure you want to delete this reception?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-user-tie fa-3x mb-3"></i>
                                                <h5>No Receptions Found</h5>
                                                <p>Start by adding your first reception.</p>
                                                <a href="{% url 'add_reception' %}" class="btn btn-primary">
                                                    <i class="fas fa-user-plus mr-2"></i>Add Reception
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}
