from calendar import month_name, monthrange
from io import BytesIO
import xlsxwriter
import datetime
import math
import logging
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.db.models import Q
from datetime import datetime
from django.db.models import Max

from .forms import *
from .models import *


def accountant_home(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered accountant_home view.")
    try:
        total_staff = Staff.objects.filter(is_active=True).count()
        active_staff = Staff.objects.filter(is_active=True, employment_type='Active').count()
        regular_staff= Staff.objects.filter(is_active=True, employment_type='Regular').count()
        contrect_staff= Staff.objects.filter(is_active=True, employment_type='Contract').count()
    except Exception as e:
        messages.error(request, f"Error fetching staff data: {e}")
        total_staff = active_staff = regular_staff = contrect_staff = 0
        logger.error(f"Error fetching staff data: {e}", exc_info=True)

    context = {
        'page_title': 'Accountant Dashboard',
        'total_staff': total_staff,
        'active_staff': active_staff,
        'regular_staff': regular_staff,
        'contrect_staff': contrect_staff,
    }
    logger.info("Rendering accountant_home with context: %s", context)

    return render(request, "accountant_template/home_content.html", context)

def accountant_view_profile(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered accountant_view_profile view for user: %s", request.user)
    accountant = get_object_or_404(Accountant, user=request.user)

    context = {
        'page_title': 'Profile',
        'accountant': accountant,
    }
    logger.info("Rendering accountant_view_profile for accountant: %s", accountant)
    return render(request, "accountant_template/accountant_view_profile.html", context)


def generate_payslip_regular(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered generate_payslip_regular view.")

    staffs = Staff.objects.select_related("user", "department", "designation", "grade").filter(
        employment_type="Regular", is_active=True
    )
    for staff in staffs:
        try:
            staff.deductions = Deductions.objects.filter(staff=staff).first()
            staff.regular_pay = RegularPay.objects.filter(staff=staff).first()
        except (Deductions.DoesNotExist, RegularPay.DoesNotExist):
            staff.deductions = 0
            staff.regular_pay= 0
            logger.warning(f"Missing deductions or regular_pay for staff: {staff}")

    latest_month = Fixed.objects.aggregate(latest_month=Max('month'))['latest_month']
    divisions_with_latest_values = Fixed.objects.filter(month=latest_month)

    context = {
        'latest_entry': divisions_with_latest_values,
        'staffs': staffs,
        'page_title': 'Generate Payslip For Regular',
    }
    logger.info("Rendering generate_payslip_regular with context: %s", context)
    return render(request, "accountant_template/generate_payslip_regular.html", context)

def generate_payslip_active(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered generate_payslip_active view.")

    staffs = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Active')
    for staff in staffs:
        try:
            staff.contract_pay = ContractPay.objects.get(staff=staff.staff)
            staff.deductions = Deductions.objects.get(staff=staff.staff)
        except (Deductions.DoesNotExist, ContractPay.DoesNotExist):
            staff.deductions = 0
            staff.contract_pay = 0
            logger.warning(f"Missing deductions or contract_pay for staff: {staff}")

    context = {
        'staffs': staffs,
        'page_title': 'Generate Payslip For Active',
    }
    logger.info("Rendering generate_payslip_active with context: %s", context)
    return render(request, "accountant_template/generate_payslip_active.html", context)

def generate_payslip_contract(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered generate_payslip_contract view.")

    staffs = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Contract')
    for staff in staffs:
        try:
            staff.contract_pay = ContractPay.objects.get(staff=staff.staff)
            staff.deductions = Deductions.objects.get(staff=staff.staff)
        except (Deductions.DoesNotExist, ContractPay.DoesNotExist):
            staff.deductions = 0
            staff.contract_pay = 0
            logger.warning(f"Missing deductions or contract_pay for staff: {staff}")

    context = {
        'staffs': staffs,
        'page_title': 'Generate Payslip For Contract',
    }
    logger.info("Rendering generate_payslip_contract with context: %s", context)
    return render(request, "accountant_template/generate_payslip_contract.html", context)


def calculate_payroll_regular(request):
    logger = logging.getLogger("payroll")
    medical_allowance = request.GET.get('give_medical', 'no')
    staff_members = CustomUser.objects.filter(user_type=3,staff__is_active=True, staff__employment_type='Regular')


    for staff in staff_members:
        try:
            fixed = Fixed.objects.filter(division=staff.staff.division).latest('month')
            month = fixed.month
        except Fixed.DoesNotExist:
            messages.error(request, "No fixed allowance data found. Please add fixed allowance data first.")
            return redirect('generate_payslip_regular')
        try:
            deductions = Deductions.objects.filter(staff=staff.staff.id).first()
            attendance = Attendance.objects.filter(staff=staff.staff.id).first()
            regular_pay = RegularPay.objects.filter(staff=staff.staff.id).first()
            grade = Grade.objects.get(id=staff.staff.grade_id)
        except (Deductions.DoesNotExist, Attendance.DoesNotExist, RegularPay.DoesNotExist, Grade.DoesNotExist) as e:
            messages.error(request, f"Missing data for {staff.get_full_name()}: {e}")
            continue
        except Exception as e:
            messages.error(request, f"Error processing payroll for {staff.get_full_name()}: {e}")
            continue

        # Handle missing objects before accessing their attributes
        if attendance is None:
            messages.error(request, f"No attendance record for {staff.get_full_name()}. Skipping.")
            continue
        if deductions is None:
            messages.error(request, f"No deductions record for {staff.get_full_name()}. Skipping.")
            continue
        if regular_pay is None:
            messages.error(request, f"No regular pay record for {staff.get_full_name()}. Skipping.")
            continue
        if grade is None:
            messages.error(request, f"No grade record for {staff.get_full_name()}. Skipping.")
            continue

        _, num_days_in_month = monthrange(month.year, month.month )

        basic_salary = staff.staff.basic_amt
        working_days = attendance.paid_days + attendance.lop
        basic = int((working_days*basic_salary)/num_days_in_month)

        total_deductions = (
            (deductions.society or 0) + (deductions.income_tax or 0) +
            (deductions.canteen or 0) + (deductions.advance or 0) +
            (deductions.insurance or 0) + (deductions.other or 0)
        )

        dp = math.ceil(basic*0.5)
        da = float(fixed.da)
        da = math.ceil((basic + dp)*da)
        hra = float(fixed.hra)
        hra = math.ceil((basic + dp)*hra)
        medical = 0

        if(medical_allowance == "yes"):
            medical = (basic + dp + da)/4
            
        gross_pay = basic + dp + da + hra + grade.conva + grade.adhoc + staff.staff.cca + regular_pay.arrears + regular_pay.other + medical

        epf = math.ceil((basic + dp + da) * 0.12)  # Assuming EPF is 12% of basic salary
        esi = math.ceil(gross_pay * 0.0175 if gross_pay <= 21000 else 0)  # ESI calculation
        total_deductions =  float(total_deductions) + epf + esi
        net_pay = gross_pay - total_deductions
        net_pay = int(max(0, net_pay)) # Ensure net pay is not negative

        payslip = Payslip(
                month=fixed.month,
                staff=staff.staff,
                basic=basic,
                da=da,
                dp=dp,
                hra=hra,
                conv=grade.conva,
                cca=staff.staff.cca,
                adhoc=grade.adhoc,
                medical=medical,
                arrears=regular_pay.arrears,
                Dother=deductions.other,
                Pother=regular_pay.other,
                gross_pay=gross_pay,
                epf=epf,
                esi=esi,
                income_tax=deductions.income_tax,
                canteen=deductions.canteen,
                society=deductions.society,
                advance=deductions.advance,
                insurance=deductions.insurance,
                total_deductions=total_deductions,
                net_pay=net_pay,
                paid_days=attendance.paid_days,
                lop=attendance.lop
            )
        try:
            payslip.save()
        except Exception as e:
            messages.error(request, f"Error processing {staff.get_full_name()}: {e}")
            logger.error(f"[Regular] Error saving payslip for {staff.get_full_name()} (ID: {staff.staff.id}): {e}", exc_info=True)

    return redirect(reverse('list_payslip_type', args=['Regular']))

def calculate_payroll_contract(request):
    staff_members = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Contract')

    for staff in staff_members:
        logger = logging.getLogger("payroll")
        try:
            contract_pay = ContractPay.objects.filter(staff=staff.staff.id).first()
            deductions = Deductions.objects.filter(staff=staff.staff.id).first()
            attendance = Attendance.objects.filter(staff=staff.staff.id).first()
        except (Deductions.DoesNotExist, Attendance.DoesNotExist, ContractPay.DoesNotExist) as e:
            messages.error(request, f"Missing data for {staff.get_full_name()}: {e}")
            continue
        except Exception as e:
            messages.error(request, f"Error processing payroll for {staff.get_full_name()}: {e}")
            continue

        # Handle missing objects before accessing their attributes
        if attendance is None:
            messages.error(request, f"No attendance record for {staff.get_full_name()}. Skipping.")
            continue
        if deductions is None:
            messages.error(request, f"No deductions record for {staff.get_full_name()}. Skipping.")
            continue
        if contract_pay is None:
            messages.error(request, f"No contract pay record for {staff.get_full_name()}. Skipping.")
            continue

        month = contract_pay.month
        _, num_days_in_month = monthrange(month.year, month.month )
        paid_days = attendance.paid_days
        lop = attendance.lop
        working_days = attendance.paid_days + attendance.lop
        basic_salary = staff.staff.basic_amt
        basic = (working_days*basic_salary)/num_days_in_month

        total_deductions = (
            (deductions.society or 0) + (deductions.income_tax or 0) +
            (deductions.canteen or 0) + (deductions.advance or 0) +
            (deductions.insurance or 0) + (deductions.other or 0)
        )

        hra = contract_pay.hra
        adhoc = contract_pay.adhoc
        arrears = contract_pay.arrears
        other = contract_pay.other
        gross_pay = basic + hra + adhoc + arrears + other 
        epf = math.ceil(basic * 0.12)
        esi = math.ceil(gross_pay * 0.0075 if gross_pay <= 21000 else 0)
        total_deductions = total_deductions + epf + esi
        net_pay = gross_pay - total_deductions
        net_pay = int(max(0, net_pay)) # Ensure net pay is not negative

        payslip = Payslip(
                month=contract_pay.month,
                staff=staff.staff,
                basic=basic,
                hra=hra,
                adhoc=contract_pay.adhoc,
                arrears=contract_pay.arrears,
                Dother=deductions.other,
                Pother=contract_pay.other,
                gross_pay=gross_pay,
                epf=epf,
                esi=esi,
                income_tax=deductions.income_tax,
                canteen=deductions.canteen,
                society=deductions.society,
                advance=deductions.advance,
                insurance=deductions.insurance,
                total_deductions=total_deductions,
                net_pay=net_pay,
                paid_days=attendance.paid_days,
                lop=attendance.lop
            )
        try:
            payslip.save()
        except Exception as e:
            messages.error(request, f"Error processing {staff.get_full_name()}: {e}")
            logger.error(f"[Contract] Error saving payslip for {staff.get_full_name()} (ID: {staff.staff.id}): {e}", exc_info=True)

    return redirect(reverse('list_payslip_type', args=['Contract']))

def calculate_payroll_active(request):
    staff_members = CustomUser.objects.filter(user_type=3, staff__is_active=True, staff__employment_type='Active')

    for staff in staff_members:
        logger = logging.getLogger("payroll")
        try:
            contract_pay = ContractPay.objects.filter(staff=staff.staff.id).first()
            deductions = Deductions.objects.filter(staff=staff.staff.id).first()
            attendance = Attendance.objects.filter(staff=staff.staff.id).first()
        except (Deductions.DoesNotExist, Attendance.DoesNotExist, ContractPay.DoesNotExist) as e:
            messages.error(request, f"Missing data for {staff.get_full_name()}: {e}")
            continue
        except Exception as e:
            messages.error(request, f"Error processing payroll for {staff.get_full_name()}: {e}")
            continue

        # Handle missing objects before accessing their attributes
        if attendance is None:
            messages.error(request, f"No attendance record for {staff.get_full_name()}. Skipping.")
            continue
        if deductions is None:
            messages.error(request, f"No deductions record for {staff.get_full_name()}. Skipping.")
            continue
        if contract_pay is None:
            messages.error(request, f"No contract pay record for {staff.get_full_name()}. Skipping.")
            continue

        month = contract_pay.month
        _, num_days_in_month = monthrange(month.year, month.month )

        working_days = attendance.paid_days + attendance.lop
        basic_salary = staff.staff.basic_amt
        basic = int((working_days*basic_salary)/num_days_in_month)

        total_deductions = (
            (deductions.society or 0) + (deductions.income_tax or 0) +
            (deductions.canteen or 0) + (deductions.advance or 0) +
            (deductions.insurance or 0) + (deductions.other or 0)
        )

        hra = contract_pay.hra
        adhoc = contract_pay.adhoc
        arrears = contract_pay.arrears
        other = contract_pay.other
        gross_pay = basic + hra + adhoc + arrears + other 
        net_pay = gross_pay - total_deductions
        net_pay = int(max(0, net_pay)) # Ensure net pay is not negative

        payslip = Payslip(
                month=contract_pay.month,
                staff=staff.staff,
                basic=basic,
                hra=hra,
                adhoc=contract_pay.adhoc,
                arrears=contract_pay.arrears,
                Dother=deductions.other,
                Pother=contract_pay.other,
                gross_pay=gross_pay,
                income_tax=deductions.income_tax,
                canteen=deductions.canteen,
                society=deductions.society,
                advance=deductions.advance,
                insurance=deductions.insurance,
                total_deductions=total_deductions,
                net_pay=net_pay,
                paid_days=attendance.paid_days,
                lop=attendance.lop
            )
        try:
            payslip.save()
        except Exception as e:
            messages.error(request, f"Error processing {staff.get_full_name()}: {e}")
            logger.error(f"[Active] Error saving payslip for {staff.get_full_name()} (ID: {staff.staff.id}): {e}", exc_info=True)

    return redirect(reverse('list_payslip_type', args=['Active']))



def staff_list(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered staff_list view.")
    employment_type_filter = request.GET.get('employment_type')
    regular_count = contract_count = active_count = 0
    if employment_type_filter:
        staffs = Staff.objects.select_related('user', 'department', 'designation').filter(
            Q(is_active=True) & Q(employment_type=employment_type_filter)
        )
    else:
        staffs = Staff.objects.select_related('user', 'department', 'designation').filter(is_active=True)
        regular_count = staffs.filter(employment_type='Regular').count()
        contract_count = staffs.filter(employment_type='Contract').count()
        active_count = staffs.filter(employment_type='Active').count()
    context = {
        'staffs': staffs,
        'regular_count': regular_count,
        'contract_count': contract_count,
        'active_count': active_count,
        'page_title': 'Staff List',
    }
    logger.info("Rendering staff_list with context: %s", context)
    return render(request, 'accountant_template/staff_list.html', context)

def get_staff_details(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered get_staff_details view for staff_id: %s", request.GET.get('id'))
    staff_id = request.GET.get('id')
    if not staff_id:
        messages.error(request, 'Staff ID not provided.')
        logger.error("Staff ID not provided in get_staff_details.")
        return redirect('staff_list')
    staff = get_object_or_404(Staff, id=staff_id)
    logger.info("Rendering staff_details_partial for staff: %s", staff)
    return render(request, 'accountant_template/staff_details_partial.html', {'staff': staff})

def payroll_summary_report(request):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered payroll_summary_report view.")
    month = datetime.now().month-1
    year = datetime.now().year
    employment_type = request.GET.get('employment_type', '')

    payslips = Payslip.objects.select_related('staff__user').filter(month__year=year, month__month=month)

    if employment_type:
        payslips = payslips.filter(staff__employment_type=employment_type)

    total_salary = sum(payslip.gross_pay for payslip in payslips)
    total_deductions = sum(payslip.total_deductions for payslip in payslips)

    context = {
        'page_title': 'Monthly Payroll Summary',
        'month': month_name[month],
        'year' : year,
        'payslips': payslips,
        'total_salary': total_salary,
        'total_deductions': total_deductions,
        'employment_type': employment_type,
    }
    logger.info("Rendering payroll_summary_report with context: %s", context)

    return render(request, 'accountant_template/payroll_summary_report.html', context)

def download_payroll_summary(request, month, year, employment_type=''):
    logger = logging.getLogger("accountant_views")
    logger.info("Entered download_payroll_summary view for month: %s, year: %s, employment_type: %s", month, year, employment_type)
    month = datetime.strptime(month, "%B").month
    payslips = Payslip.objects.select_related('staff__user').filter(
        month__year=year,
        month__month=month
    )
    if employment_type:
        payslips = payslips.filter(staff__employment_type=employment_type)

    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename="payroll_summary_{month}_{year}.xlsx"'
    output = BytesIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet("Payroll Summary")

    header_format = workbook.add_format({'bold': True, 'align': 'left','font_size': 12})
    title_format = workbook.add_format({'bold': True, 'align': 'center', 'font_size': 14})

    worksheet.merge_range('A1:Z1', 'Northern India Textile Research Association, Ghaziabad', title_format)
    worksheet.merge_range('A3:Z3', f'Pay Register for the Month {month_name[month]} {year}', title_format)

    worksheet.merge_range('H5:Q5', 'Earnings', header_format)
    worksheet.merge_range('S5:Y5', 'Deductions', header_format)

    column_headers = [
        'Sr.No','Employee Code','Employee Name','Father/Husband Name','Category','Attendance', 'Basic ⟨₹⟩', 'DP ⟨₹⟩', 'Basic+DP ⟨₹⟩', 'DA ⟨₹⟩', 'Basic+DP+DA ⟨₹⟩', 'HRA ⟨₹⟩', 'CCA ⟨₹⟩', 'Conv ⟨₹⟩',
        'Qtrly. Medical ⟨₹⟩', 'Arrear ⟨₹⟩', 'Adhoc ⟨₹⟩', 'Gross ⟨₹⟩', 'EPF ⟨₹⟩', 'ESI ⟨₹⟩','Advance ⟨₹⟩', 'Society ⟨₹⟩', 'Income Tax ⟨₹⟩', 'Canteen ⟨₹⟩', 'Total deduction ⟨₹⟩', 'Net Pay ⟨₹⟩'
    ]
    for col_num, header in enumerate(column_headers):
        worksheet.write(6, col_num, header, header_format)
        worksheet.set_column(col_num, col_num, len(header) )


    row_num = 7
    for index, payslip in enumerate(payslips):
        row_data = [
            index + 1,
            payslip.staff.emp_code,
            payslip.staff.user.get_full_name(),
            payslip.staff.user.father_name,
            payslip.staff.employment_type,
            payslip.paid_days + payslip.lop,
            payslip.basic,
            payslip.dp,
            payslip.basic + payslip.dp ,
            payslip.da,
            payslip.basic + payslip.dp + payslip.da,
            payslip.hra,
            payslip.cca,
            payslip.conv,
            payslip.medical,
            payslip.arrears,
            payslip.adhoc,
            payslip.gross_pay,
            payslip.epf,
            payslip.esi,
            payslip.advance,
            payslip.society,
            payslip.income_tax,
            payslip.canteen,
            payslip.total_deductions,
            payslip.net_pay
        ]
        for col_num, data in enumerate(row_data):
            worksheet.write(row_num, col_num, data)
        row_num += 1

    workbook.close()
    output.seek(0)
    response.write(output.read())
    logger.info("Payroll summary Excel generated and sent as response.")
    return response