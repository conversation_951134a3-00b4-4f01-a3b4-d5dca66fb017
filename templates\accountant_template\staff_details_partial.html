{% load static %}
<div class="row">
  <div class="col-md-4 text-center">
    {% if staff.user.profile_pic %}
      <img src="{{ staff.user.profile_pic.url }}" class="img-fluid rounded-circle mb-3" style="width: 120px; height: 120px;">
    {% else %}
      <img src="{% static 'dist/img/default-avatar.jpeg' %}" class="img-fluid rounded-circle mb-3" style="width: 120px; height: 120px;">
    {% endif %}
    <h5>{{ staff.user.first_name }} {{ staff.user.last_name }}</h5>
    <p class="text-muted">Employee Details</p>
  </div>
  <div class="col-md-8">
    <table class="table table-borderless">
      <tr><td><strong>Employee Code:</strong></td><td>{{ staff.emp_code }}</td></tr>
      <tr><td><strong>Full Name:</strong></td><td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td></tr>
      <tr><td><strong>Email:</strong></td><td>{{ staff.user.email }}</td></tr>
      <tr><td><strong>Department:</strong></td><td>{{ staff.department.name }}</td></tr>
      <tr><td><strong>Designation:</strong></td><td>{{ staff.designation.name }}</td></tr>
      <tr><td><strong>Employment Type:</strong></td><td>{{ staff.employment_type }}</td></tr>
      <tr>
        <td><strong>Status:</strong></td>
        <td>
          {% if staff.user.is_active %}
            <span class="badge badge-success">Active</span>
          {% else %}
            <span class="badge badge-danger">Inactive</span>
          {% endif %}
        </td>
      </tr>
    </table>
  </div>
</div>