import logging

# Get an instance of a logger
logger = logging.getLogger(__name__)
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from datetime import date, datetime, timedelta
from .forms import *
from .models import *
from django.http import JsonResponse
from django.db.models import Q

def reception_home(request):
    logger.info("Entered reception_home view")
    leave_count = LeaveRecord.objects.count()
    staff_count = Staff.objects.count()
    today = date.today()
    today_leaves = LeaveRecord.objects.filter(start_date__lte=today, end_date__gte=today).count()
    context = {
        'leave_count': leave_count,
        'staff_count': staff_count,
        'today_leaves': today_leaves,
        'user': request.user,
        'page_title': 'Reception Dashboard',
    }
    logger.info("Rendering reception_home template")
    return render(request, 'reception_template/home_content.html', context)

def reception_view_profile(request):
    logger.info("Entered reception_view_profile view")
    reception = get_object_or_404(Reception, user=request.user)
    context = {
        'page_title': 'Profile',
        'reception': reception,
    }
    logger.info("Rendering reception_view_profile template")
    return render(request, "reception_template/reception_view_profile.html", context)

@login_required
def reception_leave_list(request):
    logger.info("Entered reception_leave_list view")
    leave_records = LeaveRecord.objects.select_related('staff', 'leave_type').all().order_by('-start_date')
    context = {
        'leave_records': leave_records,
        'page_title': 'Manage Leave Records',
    }
    logger.info("Rendering reception_leave_list template")
    return render(request, 'reception_template/leave_list.html', context)

@login_required
def reception_leave_add(request):
    logger.info("Entered reception_leave_add view")
    if request.method == 'POST':
        logger.info("reception_leave_add POST request")
        form = LeaveRecordForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Leave record added successfully!')
            logger.info("Leave record added successfully")
            return redirect('reception_leave_list')
        else:
            logger.warning("Invalid data provided in reception_leave_add")
        # If form is invalid, fall through to render with errors
    else:
        form = LeaveRecordForm()
    context = {
        'form': form,
        'page_title': 'Add Leave Record',
    }
    logger.info("Rendering reception_leave_add template")
    return render(request, 'reception_template/manage_leave.html', context)

@login_required
def reception_leave_edit(request, leave_id):
    logger.info("Entered reception_leave_edit view")
    leave_record = get_object_or_404(LeaveRecord, id=leave_id)
    if request.method == 'POST':
        logger.info("reception_leave_edit POST request")
        form = LeaveRecordForm(request.POST, instance=leave_record)
        if form.is_valid():
            form.save()
            messages.success(request, 'Leave record updated successfully!')
            logger.info("Leave record updated successfully for id %s", leave_id)
            return redirect('reception_leave_list')
        else:
            logger.warning("Invalid data provided in reception_leave_edit for id %s", leave_id)
        # If form is invalid, fall through to render with errors
    else:
        form = LeaveRecordForm(instance=leave_record)
    context = {
        'form': form,
        'page_title': 'Edit Leave Record',
    }
    logger.info("Rendering reception_leave_edit template")
    return render(request, 'reception_template/manage_leave.html', context)

@login_required
def reception_leave_delete(request, leave_id):
    logger.info("Entered reception_leave_delete view")
    leave_record = get_object_or_404(LeaveRecord, id=leave_id)
    leave_record.delete()
    messages.success(request, 'Leave record deleted successfully!')
    logger.info("Leave record deleted for id %s", leave_id)
    return redirect('reception_leave_list')

@login_required
def staff_search(request):
    logger.info("Entered staff_search view")
    term = request.GET.get('q', '')
    staff = Staff.objects.filter(
        Q(is_active=True) & (
            Q(emp_code__icontains=term) |
            Q(user__email__icontains=term) |
            Q(user__first_name__icontains=term) |
            Q(user__last_name__icontains=term)
        )
    )[:20]  # Limit to 20 results for performance
    results = [
        {'id': s.id, 'text': f"{s.emp_code} - {s.user.get_full_name()} ({s.user.email})"}
        for s in staff
    ]
    logger.info("staff_search returned %d results for term '%s'", len(results), term)
    return JsonResponse({'results': results})