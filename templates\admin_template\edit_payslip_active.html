{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'plugins/bs-stepper/css/bs-stepper.min.css' %}">
{% endblock extra_css %}

{% block content %}
<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{{page_title}}</h3>
          </div>
          <div class="card-body p-0">
            <div class="bs-stepper">
              <div class="bs-stepper-header" role="tablist">
                <div class="step" data-target="#deductions-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="deductions-part"
                    id="deductions-part-trigger">
                    <span class="bs-stepper-circle">1</span>
                    <span class="bs-stepper-label">Deductions</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#paid-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="paid-part"
                    id="paid-part-trigger">
                    <span class="bs-stepper-circle">2</span>
                    <span class="bs-stepper-label">Pad Values</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#attendence-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="attendence-part"
                    id="attendence-part-trigger">
                    <span class="bs-stepper-circle">3</span>
                    <span class="bs-stepper-label">Attendence</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#generate-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="generate-part"
                    id="generate-part-trigger">
                    <span class="bs-stepper-circle">4</span>
                    <span class="bs-stepper-label">Generate</span>
                  </button>
                </div>
              </div>
              <div class="bs-stepper-content">
                <div id="deductions-part" class="content active bs-stepper-content" role="tabpanel"
                  aria-labelledby="deductions-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title">Enter Deduction value </h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                          <table id="example1" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th>EmpCode</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Designation</th>
                                <th>Society (₹)</th>
                                <th>Income Tax (₹)</th>
                                <th>Canteen (₹)</th>
                                <th>Advance (₹)</th>
                                <th>Insurance (₹)</th>
                                <th>Miscellaneous (₹)</th>
                                <th>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form">
                                  {% csrf_token %}
                                  <td>{{ staff.emp_code}}</td>
                                  <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                  <td>{{ staff.department.name }}</td>
                                  <td>{{ staff.designation.name }}</td>
                                  <td><input type="text" name="society" id="society_id"
                                      value="{{staff.deductions.society}}" class="form-control" required></td>
                                  <td><input type="text" name="income_tax" id="income_tax_id"
                                      value="{{staff.deductions.income_tax}}" class="form-control" required>
                                  </td>
                                  <td><input type="text" name="canteen" id="canteen_id"
                                      value="{{staff.deductions.canteen}}" class="form-control" required></td>
                                  <td><input type="text" name="advance" id="advance_id"
                                      value="{{staff.deductions.advance}}" class="form-control" required></td>
                                  <td><input type="text" name="insurance" id="insurance_id"
                                      value="{{staff.deductions.insurance}}" class="form-control" required>
                                  </td>
                                  <td><input type="text" name="other" id="other_id" value="{{staff.deductions.other}}"
                                      class="form-control" required>
                                  </td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                    <button type="button" class="btn btn-success save-btn"><i
                                        class="fas fa-save mr-2"> Save</i></button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <!-- /.col -->
                  </div>
                  <!-- /.row -->
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="paid-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="paid-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title">Enter Pay Values</h3>
                        </div>
                        <div class="card-body">
                          <table id="example2" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th>EmpCode</th>
                                <th>Name</th>
                                <th>Department </th>
                                <th>Designation </th>
                                <th>Month </th>
                                <th>HRA (₹)</th>
                                <th>Adhoc (₹)</th>
                                <th>Arrears (₹)</th>
                                <th>Advance (₹)</th>
                                <th>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form">
                                  {% csrf_token %}
                                  <td>{{ staff.emp_code}}</td>
                                  <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                  <td>{{ staff.department.name }}</td>
                                  <td>{{ staff.designation.name }}</td>
                                  <td>
                                    <input type="hidden" id="staff_id" name="staff_id" value="{{ staff.id }}">
                                    <input type="month" class="form-control monthpicker"
                                      id="monthpicker{{ staff.id }}" name="month" required>
                                  </td>
                                  <td><input type="text" name="hra" id="hra_id" value="{{staff.contract_pay.hra}}"
                                      class="form-control" required></td>
                                  <td><input type="text" name="adhoc" id="adhoc_id" value="{{staff.contract_pay.adhoc}}"
                                      class="form-control" required></td>
                                  <td><input type="text" name="arrears" id="arrears_id" value="{{staff.contract_pay.arrears}}"
                                      class="form-control" required></td>
                                  <td><input type="text" name="other" id="other_id" value="{{staff.contract_pay.other}}"
                                      class="form-control" required></td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                    <button type="button" class="btn btn-success save-btn-pay"><i
                                        class="fas fa-save mr-2"></i> Save</button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>

                      </div>
                    </div>
                  </div>
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="attendence-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="attendence-part-trigger">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title">Enter Attendance</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                          <table id="example3" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                              <tr>
                                <th>EmpCode</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Designation</th>
                                <th>Paid Days</th>
                                <th>Leave of Paid</th>
                                <th>Action</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for staff in staffs %}
                              <tr>
                                <form class="staff-form" method="post" action="{% url 'save_attendence_details' %}">
                                  {% csrf_token %}
                                  <td>{{ staff.emp_code }}</td>
                                  <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                  <td>{{ staff.department.name }}</td>
                                  <td>{{ staff.designation.name }}</td>
                                  <td>
                                    <input type="text" name="paid_days" id="paid_days_id_{{ forloop.counter }}"
                                      value="{{ staff.attendance.paid_days }}" class="form-control" required>
                                  </td>
                                  <td>
                                    <input type="text" name="lop" id="lop_id_{{ forloop.counter }}"
                                      value="{{ staff.attendance.lop }}" class="form-control" required>
                                  </td>
                                  <td>
                                    <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                    <button type="button" class="btn btn-success save-btn-attendence"><i
                                        class="fas fa-save mr-2"></i>Save</button>
                                  </td>
                                </form>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <!-- /.col -->
                  </div>
                  <!-- /.row -->
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                  <button class="btn btn-primary" onclick="stepper.next()">Next</button>
                </div>

                <div id="generate-part" class="content bs-stepper-content" role="tabpanel"
                  aria-labelledby="generate-part-trigger">
                  <div class="row">
                    <div class="col-md-3"></div>
                    <div class="col-md-6">
                      <div class="card card-primary card-outline">
                        <div class="card-header">
                          <h3 class="card-title">Calculate PayRoll</h3>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">

                          <button type="button" id="calculate-payroll-btn" class="btn btn-success">
                            <i class="fas fa-calculator mr-2"></i> Calculate Payroll
                          </button>

                        </div>
                        <!-- /.card-body -->
                      </div>
                      <!-- /.card -->
                    </div>
                    <div class="col-md-3"></div>
                  </div>
                  <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- /.row -->
</section>
{% endblock content %}

{% block extra_js %}
<!-- BS-Stepper -->
<script src="{% static 'plugins/bs-stepper/js/bs-stepper.min.js' %}"></script>
<script src="{% static 'plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    window.stepper = new Stepper(document.querySelector('.bs-stepper'));
    const currentDate = new Date();
    const previousMonth = new Date(currentDate.setMonth(currentDate.getMonth() - 1));
    const previousMonthString = previousMonth.toISOString().split('T')[0].slice(0, 7);

    const monthPickers = document.querySelectorAll('.monthpicker');
    monthPickers.forEach(monthPicker => {
      monthPicker.value = previousMonthString;
    });
  });

  function getDaysInPreviousMonth() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const lastDayOfPreviousMonth = new Date(year, month, 0).getDate();
    return lastDayOfPreviousMonth;
  }

  const previousMonthDays = getDaysInPreviousMonth();

  document.querySelectorAll('input[name="paid_days"]').forEach(input => {
    if (!input.value) {
      input.value = previousMonthDays;
    }
  });

  document.querySelectorAll('input[name="lop"],input[name="arrears"],input[name="other"],input[name="hra"],input[name="adhoc"]').forEach(input => {
    if (!input.value) {
      input.value = 0;
    }
  });

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  const csrftoken = getCookie('csrftoken');

  $.ajaxSetup({
    beforeSend: function (xhr, settings) {
      if (!/^(GET|HEAD|OPTIONS|TRACE)$/.test(settings.type) && !this.crossDomain) {
        xhr.setRequestHeader("X-CSRFToken", csrftoken);
      }
    }
  });


  $(document).ready(function () {
    $("#example1").on("click", ".save-btn", function() {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_deduction_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example2").on("click", ".save-btn-pay", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_contract_pay" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example3").on("click", ".save-btn-attendence", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_attendence_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  document.getElementById('calculate-payroll-btn').addEventListener('click', function () {
    const url = `{% url 'edit_payroll_active' %}`;
    window.location.href = url;
  });

</script>
{% endblock extra_js %}