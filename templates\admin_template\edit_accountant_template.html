{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-dark card-outline">
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>
          </div>

          <div class="card-body">
            <div class="row">
              <div class="col-md-4">
                <div class="card card-primary card-outline">
                  <div class="card-body box-profile">
                    <div class="text-center">
                      {% if accountant.user.profile_pic %}
                        <img class="profile-user-img img-fluid img-circle"
                             src="{{ accountant.user.profile_pic }}"
                             alt="User profile picture">
                      {% else %}
                        <img class="profile-user-img img-fluid img-circle"
                             src="{% static 'dist/img/default-avatar.jpeg' %}"  
                             alt="User profile picture">
                      {% endif %}
                    </div>

                    <h3 class="profile-username text-center">{{ accountant.user.first_name }} {{ accountant.user.last_name }}</h3>
                    <p class="text-muted text-center">Accountant</p>

                    <ul class="list-group list-group-unbordered mb-3">
                      <li class="list-group-item">
                        <b>Email</b> <a class="float-right">{{ accountant.user.email }}</a>
                      </li>
                      <li class="list-group-item">
                        <b>Father's Name</b> <a class="float-right">{{ accountant.user.father_name }}</a>
                      </li>
                      <li class="list-group-item">
                        <b>Gender</b> <a class="float-right">{{ accountant.user.gender }}</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div class="col-md-8">
                {% include "main_app/form_template.html" with form=form button_text="Update Accountant" %}
              </div>
            </div>
          </div>
          
        </div>
      </div> 
    </div>
  </div>
</section>
{% endblock content %}
