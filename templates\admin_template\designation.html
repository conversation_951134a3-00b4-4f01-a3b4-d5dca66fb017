{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Designation Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-id-badge mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_designation' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Designation
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'designation' %}">
                                        <i class="fas fa-list mr-2"></i>All Designations
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example3" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Department</th>
                                        <th>Designation Name</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for designation in designations %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">
                                                <i class="fas fa-building mr-1"></i>{{ designation.department.name }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <i class="fas fa-id-badge text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ designation.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-building mr-1"></i>{{ designation.department.name }}
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_designation' designation.id %}" class="btn btn-info btn-sm" title="Edit Designation">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_designation' designation.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Designation" data-confirm="Are you sure you want to delete this designation?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-id-badge fa-3x mb-3"></i>
                                                <h5>No Designations Found</h5>
                                                <p>Start by adding your first designation.</p>
                                                <a href="{% url 'manage_designation' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Designation
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}