{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock page_title %}
{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Grade Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-layer-group mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_grade' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Grade
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'grade' %}">
                                        <i class="fas fa-list mr-2"></i>All Grades
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-download mr-2"></i>Export Data
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Grade Name</th>
                                        <th>Salary Range</th>
                                        <th>Increment</th>
                                        <th>Adhoc</th>
                                        <th>Conva</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for grade in grades %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="mr-3">
                                                    <div class="bg-warning rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                        <i class="fas fa-layer-group text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ grade.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-money-bill-wave mr-1"></i>Salary Grade
                                                    </small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                <i class="fas fa-rupee-sign mr-1"></i>{{ grade.start|floatformat:0 }} - {{ grade.end|floatformat:0 }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                <i class="fas fa-arrow-up mr-1"></i>₹{{ grade.increment|floatformat:0 }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">
                                                <i class="fas fa-plus mr-1"></i>₹{{ grade.adhoc|floatformat:0 }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">
                                                <i class="fas fa-coins mr-1"></i>₹{{ grade.conva|floatformat:0 }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_grade' grade.id %}" class="btn btn-info btn-sm" title="Edit Grade">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_grade' grade.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Grade" data-confirm="Are you sure you want to delete this grade?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-layer-group fa-3x mb-3"></i>
                                                <h5>No Grades Found</h5>
                                                <p>Start by adding your first salary grade.</p>
                                                <a href="{% url 'manage_grade' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Grade
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

