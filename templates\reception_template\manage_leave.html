{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block content %}
<section class="content">
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-plus mr-2"></i>{{ page_title }}
                        </h3>
                    </div>
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <div class="card-body">
                            {% for field in form %}
                                <div class="form-group">
                                    {{ field.label_tag }}
                                    {{ field }}
                                    {% if field.help_text %}
                                        <small class="form-text text-muted">{{ field.help_text }}</small>
                                    {% endif %}
                                    {% for error in field.errors %}
                                        <div class="text-danger small">{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                        <div class="card-footer text-right">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-1"></i> Save
                            </button>
                            <a href="{% url 'reception_leave_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
