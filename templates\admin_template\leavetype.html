{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Manage Leave Type{% endblock page_title %}

{% block content %}

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Enhanced Leave Type Management Card -->
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-percentage mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <div class="btn-group">
                                <a href="{% url 'manage_leavetype' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus mr-1"></i> Add New Leave Type 
                                </a>
                                <button type="button" class="btn btn-info btn-sm dropdown-toggle dropdown-toggle-split" data-toggle="dropdown">
                                    <span class="sr-only">Toggle Dropdown</span>
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{% url 'leavetype' %}">
                                        <i class="fas fa-list mr-2"></i>All Allowances
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Enhanced DataTable -->
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Leave Type</th>
                                        <th>Description</th>
                                        <th style="width: 150px;" class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for leavetype in leavetypes %}
                                    <tr>
                                        <td>
                                            <span class="badge badge-secondary">{{ forloop.counter }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ leavetype.name }}</strong>
                                        </td>
                                        <td>
                                            {{ leavetype.description|default:'-' }}
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'manage_leavetype' leavetype.id %}" class="btn btn-info btn-sm" title="Edit Leave Type">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'delete_leavetype' leavetype.id %}" class="btn btn-danger btn-sm"
                                                   title="Delete Leave Type" data-confirm="Are you sure you want to delete this leave type?">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            <div class="py-4">
                                                <i class="fas fa-percentage fa-3x mb-3"></i>
                                                <h5>No Leave Type Found</h5>
                                                <p>Start by adding your first leave type.</p>
                                                <a href="{% url 'manage_leavetype' %}" class="btn btn-primary">
                                                    <i class="fas fa-plus mr-2"></i>Add Leave Type
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}
