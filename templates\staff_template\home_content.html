{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Staff Dashboard{% endblock page_title %}
{% block extra_css %}
<style>
  /* Custom CSS for the dashboard */
  .card-info .card-header {
    background-color: #17a2b8; /* Brighter blue for the header */
    color: white;
  }

  .list-group-item {
    border-bottom: 1px solid #f0f0f0; /* Softer border for list items */
  }
</style>
{% endblock extra_css %}

{% block content %}
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-4 col-12 mb-3">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-info elevation-1">
            <i class="fas fa-calendar-check"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">My Leave Records</span>
            <span class="info-box-number">{{ leave_count }}</span>
            <div class="progress">
              <div class="progress-bar bg-info" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_leave_list' %}" class="text-info">View Details</a>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-12 mb-3">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-success elevation-1">
            <i class="fas fa-money-bill-wave"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Recent Payslips</span>
            <span class="info-box-number">{{ recent_payslips|length }}</span>
            <div class="progress">
              <div class="progress-bar bg-success" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list_payslips' %}" class="text-success">View Payslips</a>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-4 col-12 mb-3">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-primary elevation-1">
            <i class="fas fa-user"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">My Profile</span>
            <span class="info-box-number">&nbsp;</span>
            <div class="progress">
              <div class="progress-bar bg-primary" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_view_profile' %}" class="text-primary">View Profile</a>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <div class="card card-info">
          <div class="card-header">
            <h3 class="card-title">Welcome, {{ user.first_name }}!</h3>
          </div>
          <div class="card-body">
            {% if recent_payslips %}
              <p>Here are your recent payslips from {{ start_date }} to {{ end_date }}:</p>
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Month</th>
                    <th>Net Pay</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for payslip in recent_payslips %}
                    <tr>
                      <td>{{ payslip.month|date:'F Y' }}</td>
                      <td>₹{{ payslip.net_pay }}</td>
                      <td>
                        <a href="{% url 'view_payslip_staff' user.staff.id payslip.month|date:'Y-m-d' %}" 
                          class="btn btn-sm btn-info" title="View Payslip">
                          <i class="fas fa-eye"></i>
                        </a> 
                        </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            {% else %}
              <p>No payslips found for the selected period.</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock content %}
