{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Reception Dashboard{% endblock page_title %}

{% block content %}
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-info elevation-1">
            <i class="fas fa-calendar-check"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Total Leave Records</span>
            <span class="info-box-number">{{ leave_count }}</span>
            <div class="progress">
              <div class="progress-bar bg-info" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'reception_leave_list' %}" class="text-info">View Details</a>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-success elevation-1">
            <i class="fas fa-users"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Total Staff</span>
            <span class="info-box-number">{{ staff_count }}</span>
            <div class="progress">
              <div class="progress-bar bg-success" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'staff_list' %}" class="text-success">View Staff</a>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-warning elevation-1">
            <i class="fas fa-user-clock"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Today's Leaves</span>
            <span class="info-box-number">{{ today_leaves }}</span>
            <div class="progress">
              <div class="progress-bar bg-warning" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'reception_leave_list' %}" class="text-warning">Today's Leave Records</a>
            </span>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-6">
        <div class="info-box stats-card">
          <span class="info-box-icon bg-danger elevation-1">
            <i class="fas fa-user-plus"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">Active Reception</span>
            <span class="info-box-number">1</span>
            <div class="progress">
              <div class="progress-bar bg-danger" style="width: 100%"></div>
            </div>
            <span class="progress-description">
              <a href="{% url 'reception_view_profile' %}" class="text-danger">My Profile</a>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-12">
        <div class="card card-info">
          <div class="card-header">
            <h3 class="card-title">Welcome, {{ user.first_name }}!</h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock content %}
