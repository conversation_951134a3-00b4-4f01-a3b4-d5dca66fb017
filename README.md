# Payroll Management System (PMS)

A Django-based Payroll Management System for managing employee payrolls, departments, and administrative tasks.

## Features

- Employee management (add, edit, view, and delete staff, accountants, and receptionists)
- Payroll generation for Regular, Contract, and Active staff
- Department, Designation, and Division management
- Role-based access: Admin, Accountant, Reception, Staff
- Leave management and leave records
- Email notifications for payroll and leave
- Secure authentication with a custom user model and custom email backend
- Google reCAPTCHA integration (for production)
- Responsive UI with Bootstrap and AdminLTE
- DataTables integration for reports and lists

## Prerequisites

- Python 3.12+
- Django 4.x+
- MySQL database (or SQLite for development)
- Git

## Quick Setup

### 1. Clone and Setup Environment

```bash
# Clone the repository (if not already done)
git clone https://github.com/apoorv-deep/PMS.git

cd PMS
```

### 2. Create and Activate Virtual Environment

```bash
python3 -m venv venv

source venv/bin/activate  # On Linux/Mac

venv\Scripts\activate  # On Windows

pip install -r requirements.txt
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory with the following content:

```env
SECRET_KEY=your-generated-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=3306

EMAIL_HOST=smtp.gmail.com
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your-app-password

GOOGLE_RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key
GOOGLE_RECAPTCHA_SITE_KEY=your-recaptcha-site-key
```

For development, you can use SQLite by updating `PMS/settings.py` accordingly.

### 4. Database Setup

```bash
python manage.py makemigrations

python manage.py migrate

python manage.py createsuperuser
```

### 5. Run Development Server

```bash
python manage.py runserver
```

Visit `http://127.0.0.1:8000` to access the application.

## Project Structure

```
PMS/
├── PMS/                    # Django project settings and wsgi/asgi
├── main/                   # Main application (views, models, forms, etc.)
│   ├── backends/           # Custom authentication backends and middleware
│   ├── migrations/         # Django migrations
│   ├── templatetags/       # Custom template filters
├── templates/              # HTML templates (role-based folders)
├── static/                 # Static files (CSS, JS, images, plugins)
├── db.sqlite3              # SQLite database (for development)
├── requirements.txt        # Python dependencies
├── manage.py               # Django management script
└── README.md               # This file
```

## User Roles

1. **Admin**: Full system access, user and department management, payroll oversight
2. **Accountant**: Payroll generation and management, view staff details
3. **Reception**: Staff attendance, leave management, basic staff info
4. **Staff**: View personal payroll, leave records, and profile

## Development

### Collecting Static Files

```bash
python manage.py collectstatic
```

### Creating New Migrations

```bash
python manage.py makemigrations

python manage.py migrate
```

## Production Deployment

1. Set `DEBUG=False` in `.env`
2. Configure proper `ALLOWED_HOSTS`
3. Set up production database and email configurations
4. Configure Google reCAPTCHA keys
5. Use a production web server (nginx + gunicorn or similar)
6. Set up SSL certificates

## Troubleshooting

### Common Issues

1. **Database Connection Error**: Check your database credentials in `.env` and ensure the database server is running.
2. **Email Not Working**: Verify email settings or use Django's console backend for development.
3. **Static Files Not Loading**: Run `python manage.py collectstatic` and check static file settings.
4. **Login/Authentication Issues**: Ensure custom user model and authentication backends are configured correctly.

### Support

For issues and questions, please check the documentation or create an issue in the repository.
