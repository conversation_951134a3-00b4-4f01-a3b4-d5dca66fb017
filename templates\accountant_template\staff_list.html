{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Staff Directory{% endblock page_title %}

{% block extra_css %}
<style>
  .staff-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }
  .employment-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
  }
  .filter-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .stats-row {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }
  .stat-item {
    text-align: center;
    padding: 10px;
  }
  .stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
  }
  .stat-label {
    color: #6c757d;
    font-size: 0.9rem;
  }
  .table-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0 2px;
  }
  .staff-name {
    font-weight: 600;
    color: #495057;
  }
  .staff-email {
    color: #6c757d;
    font-size: 0.9rem;
  }
</style>
{% endblock extra_css %}

{% block content %}

<section class="content">
  <div class="container-fluid">

    <!-- Statistics Row -->
    <div class="stats-row">
      <div class="row">
        <div class="col-md-3 stat-item">
          <div class="stat-number">{{ staffs|length }}</div>
          <div class="stat-label">Total Staff</div>
        </div>
        <div class="col-md-3 stat-item">
          <div class="stat-number">{{ regular_count }}</div>
          <div class="stat-label">Regular</div>
        </div>
        <div class="col-md-3 stat-item">
          <div class="stat-number">{{ contract_count }}</div>
          <div class="stat-label">Contract</div>
        </div>
        <div class="col-md-3 stat-item">
          <div class="stat-number">{{ active_count }}</div>
          <div class="stat-label">Active</div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="row mb-3">
      <div class="col-12">
        <div class="card filter-card">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-md-3">
                <label for="employmentFilter" class="form-label">Filter by Employment Type:</label>
                <select id="employmentFilter" class="form-control">
                  <option value="">All Types</option>
                  <option value="Regular">Regular</option>
                  <option value="Contract">Contract</option>
                  <option value="Active">Active</option>
                </select>
              </div>
              <div class="col-md-3">
                <label for="statusFilter" class="form-label">Filter by Status:</label>
                <select id="statusFilter" class="form-control">
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div class="col-md-3">
                <label for="departmentFilter" class="form-label">Filter by Department:</label>
                <select id="departmentFilter" class="form-control">
                  <option value="">All Departments</option>
                  {% for staff in staffs %}
                    <option value="{{ staff.department.name }}">{{ staff.department.name }}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="col-md-3 d-flex align-items-end">
                <button id="clearFilters" class="btn btn-outline-secondary">
                  <i class="fas fa-times mr-1"></i>Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Staff Table -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-users mr-2"></i>
              {{ page_title }}
            </h3>
            <div class="card-tools">
              <button type="button" class="btn btn-primary btn-sm" onclick="window.print()">
                <i class="fas fa-print mr-1"></i>Print List
              </button>
            </div>
          </div>

          <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="close" data-dismiss="alert">
                            <span>&times;</span>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="table-responsive">
              <table id="example1" class="table table-hover">
                <thead class="thead-light">
                  <tr>
                    <th width="5%">#</th>
                    <!-- <th width="10%">Photo</th> -->
                    <th width="10%">Emp Code</th>
                    <th width="20%">Name & Email</th>
                    <th width="15%">Department</th>
                    <th width="15%">Designation</th>
                    <th width="12%">Employment Type</th>
                    <th width="8%">Status</th>
                    <th width="5%">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for staff in staffs %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <!-- <td>
                      {% if staff.user.profile_pic %}
                        <img src="{{ staff.user.profile_pic }}" alt="Profile" class="staff-avatar">
                      {% else %}
                        <div class="staff-avatar bg-primary d-flex align-items-center justify-content-center text-white">
                          {{ staff.user.first_name|first }}{{ staff.user.last_name|first }}
                        </div>
                      {% endif %}
                    </td> -->
                    <td>
                      <span class="badge badge-outline-primary">{{ staff.emp_code }}</span>
                    </td>
                    <td>
                      <div class="staff-name">{{ staff.user.first_name }} {{ staff.user.last_name }}</div>
                      <div class="staff-email">{{ staff.user.email }}</div>
                    </td>
                    <td>
                      <span class="badge badge-light">{{ staff.department.name }}</span>
                    </td>
                    <td>
                      <span class="badge badge-info">{{ staff.designation.name }}</span>
                    </td>
                    <td>
                      {% if staff.employment_type == 'Regular' %}
                        <span class="badge badge-primary employment-badge">{{ staff.employment_type }}</span>
                      {% elif staff.employment_type == 'Contract' %}
                        <span class="badge badge-warning employment-badge">{{ staff.employment_type }}</span>
                      {% else %}
                        <span class="badge badge-success employment-badge">{{ staff.employment_type }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if staff.user.is_active %}
                        <span class="badge badge-success">
                          <i class="fas fa-check-circle mr-1"></i>Active
                        </span>
                      {% else %}
                        <span class="badge badge-danger">
                          <i class="fas fa-times-circle mr-1"></i>Inactive
                        </span>
                      {% endif %}
                    </td>
                    <td class="table-actions">
                      <button class="btn btn-info btn-sm" title="View Details" onclick="viewStaffDetails('{{ staff.id }}')">
                        <i class="fas fa-eye"></i>
                      </button>
                    </td>
                  </tr>
                  {% empty %}
                  <tr>
                    <td colspan="9" class="text-center py-4">
                      <i class="fas fa-users fa-3x text-muted mb-3"></i>
                      <h5 class="text-muted">No staff members found</h5>
                      <p class="text-muted">Try adjusting your filters or contact administrator.</p>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Staff Details Modal -->
<div class="modal fade" id="staffDetailsModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-user mr-2"></i>Staff Details
        </h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body" id="staffDetailsContent">
        <!-- Content will be loaded here -->
      </div>
    </div>
  </div>
</div>

{% endblock content %}

{% block extra_js %}
<script>
$(document).ready(function() {
  // Enhanced filtering
  $('#employmentFilter, #statusFilter, #departmentFilter').on('change', function() {
    var table = $('#example1').DataTable();

    var employmentType = $('#employmentFilter').val();
    var status = $('#statusFilter').val();
    var department = $('#departmentFilter').val();

    // Clear previous search
    table.search('').columns().search('').draw();

    // Apply filters
    if (employmentType) {
      table.column(5).search(employmentType);
    }
    if (status) {
      if (status === 'active') {
        table.column(6).search('Active');
      } else {
        table.column(6).search('Inactive');
      }
    }
    if (department) {
      table.column(3).search(department);
    }

    table.draw();
  });

  // Clear filters
  $('#clearFilters').on('click', function() {
    $('#employmentFilter, #statusFilter, #departmentFilter').val('');
    var table = $('#example1').DataTable();
    table.search('').columns().search('').draw();
  });
});

function viewStaffDetails(staffId) {
  $('#staffDetailsContent').html(`
    <div class="text-center">
      <i class="fas fa-spinner fa-spin fa-2x"></i>
      <p class="mt-2">Loading staff details...</p>
    </div>
  `);
  $('#staffDetailsModal').modal('show');

  // AJAX call to fetch staff details
  $.ajax({
    url: '/accountant/get_staff_details/',
    method: 'GET',
    data: { id: staffId },
    success: function(response) {
      // If you return HTML from the backend
      $('#staffDetailsContent').html(response);
    },
    error: function() {
      $('#staffDetailsContent').html(`
        <div class="alert alert-danger">Failed to load staff details. Please try again.</div>
      `);
    }
  });
}
</script>
{% endblock extra_js %}

