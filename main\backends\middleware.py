from django.utils.deprecation import MiddlewareMixin
from django.urls import reverse
from django.shortcuts import redirect

class LoginCheckMiddleWare(MiddlewareMixin):
    def process_view(self, request, view_func, view_args, view_kwargs):
        modulename = view_func.__module__
        user = request.user

        if user.is_authenticated:
        # User type-based redirection (assuming user_type is an integer in CustomUser)
            if user.user_type == 1:  # Admin
                if modulename == "main.staff_views" or modulename == "main.accountant_views" or modulename == "main.reception_views":
                    return redirect(reverse('admin_home'))
            elif user.user_type == 2:  # Accountant
                if modulename == "main.staff_views" or modulename == "main.admin_views" or modulename == "main.reception_views":
                    return redirect(reverse('accountant_home'))
            elif user.user_type == 3:  # Staff
                if modulename == "main.admin_views" or modulename == "main.accountant_views" or modulename == "main.reception_views":
                    return redirect(reverse('staff_home'))
            elif user.user_type == 4:  # reception
                if modulename == "main.admin_views" or modulename == "main.accountant_views" or modulename == "main.staff_views":
                    return redirect(reverse('reception_home'))
            else: # None of the aforementioned ? Please take the user to login page
                return redirect(reverse('login_page'))
        else:
            if request.path == reverse('login_page') or modulename == 'django.contrib.auth.views' or request.path == reverse('user_login'): # If the path is login or has anything to do with authentication, pass
                pass
            else:
                return redirect(reverse('login_page'))