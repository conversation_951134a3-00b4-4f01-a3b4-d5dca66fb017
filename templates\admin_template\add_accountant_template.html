{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Add Accountant{% endblock page_title %}

{% block content %}
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary card-outline">
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>
          </div>
          <div class="card-body">
            {% include "main_app/form_template.html" with form=form button_text="Add Accountant" %}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
{% endblock content %}

{% block extra_js %}
<script>
  function validateEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  $(document).ready(function () {
    $("#id_email").keyup(function () {
      var email = $(this).val();
      if (validateEmail(email)) {
        $.ajax({
          url: "{% url 'check_email_availability' %}",
          type: 'POST',
          data: { email: email },
          headers: {
            'X-CSRFToken': $('input[name=csrfmiddlewaretoken]').val()
          },
        }).done(function (response) {
          $(".email_error").show(); // Always show the message container
          if (response == "True") {
            $(".email_error").text("Email Address Already Exists").removeClass("valid").addClass("text-danger");
            $("#id_email").removeClass("is-valid").addClass("is-invalid");
          } else {
            $(".email_error").text("Email Address Available").removeClass("text-danger").addClass("text-success");
            $("#id_email").removeClass("is-invalid").addClass("is-valid");
          }
        }).fail(function (response) {
          $(".email_error").text("Server Could Not Process This").removeClass("valid").addClass("text-danger");
          $("#id_email").removeClass("is-valid").addClass("is-invalid");
        });
      } else {
        $(".email_error").hide(); // Hide the message container if email is invalid
        $("#id_email").removeClass("is-valid is-invalid");
      }
    });
  });
</script>
{% endblock extra_js %}