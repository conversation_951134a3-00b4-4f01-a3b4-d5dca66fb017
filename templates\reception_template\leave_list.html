{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block content %}
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card card-primary card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-alt mr-2"></i>{{ page_title }}
                        </h3>
                        <div class="card-tools">
                            <a href="{% url 'reception_leave_add' %}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus mr-1"></i> Add Leave Record
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Staff</th>
                                        <th>Leave Type</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Day Type</th>
                                        <th>Leave Period</th>
                                        <th>Reason</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for leave in leave_records %}
                                    <tr>
                                        <td>{{ forloop.counter }}</td>
                                        <td>{{ leave.staff.user.get_full_name }}</td>
                                        <td>{{ leave.leave_type.name }}</td>
                                        <td>{{ leave.start_date }}</td>
                                        <td>{{ leave.end_date }}</td>
                                        <td>{{ leave.get_time_display }}</td>
                                        <td>{{ leave.leave_period }}</td>
                                        <td>{{ leave.reason|default:'-' }}</td>
                                        <td class="text-center">
                                            <a href="{% url 'reception_leave_edit' leave.id %}" class="btn btn-info btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'reception_leave_delete' leave.id %}" class="btn btn-danger btn-sm" title="Delete" onclick="return confirm('Are you sure you want to delete this leave record?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">No leave records found.</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
