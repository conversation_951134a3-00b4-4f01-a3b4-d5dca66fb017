{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}List Of Payslips{% endblock page_title %}

{% block content %}
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card card-info card-outline">
          <div class="card-header">
            <h3 class="card-title">{{ page_title }}</h3>
          </div>
          <div class="card-body">
            <table id="example1" class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>Month</th>
                  <th>Basic Salary (₹)</th>
                  <th>Gross Pay (₹)</th>
                  <th>Total Deductions (₹)</th>
                  <th>Net Pay (₹)</th>
                  <th class="text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for payslip in payslips %}
                <tr>
                  <td>{{ payslip.month|date:"F Y" }}</td>
                  <td>{{ payslip.basic }}</td>
                  <td>{{ payslip.gross_pay }}</td>
                  <td>{{ payslip.total_deductions }}</td>
                  <td>{{ payslip.net_pay }}</td>
                  <td class="text-center">
                    <a href="{% url 'view_payslip' user.staff.id payslip.month|date:'Y-m-d' %}" class="btn btn-success btn-sm">
                      <i class="fas fa-eye"></i> View
                    </a>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{% endblock content %}


