{% extends 'main_app/base.html' %}
{% load static %}

{% block page_title %}Admin Dashboard{% endblock page_title %}

{% block content %}
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Dashboard</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active">Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1">
                        <i class="fas fa-user-tie"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Accountants</span>
                        <span class="info-box-number">{{ total_accountant }}</span>
                        <a href="{% url 'manage_accountant' %}" class="small-box-footer text-info">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-primary elevation-1">
                        <i class="fas fa-users"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Staff</span>
                        <span class="info-box-number">{{ total_staff }}</span>
                        <a href="{% url 'manage_staff' %}" class="small-box-footer text-primary">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1">
                        <i class="fas fa-user-check"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Active Staff</span>
                        <span class="info-box-number">{{ active_staff }}</span>
                        <a href="{% url 'manage_staff' %}?is_active=True" class="small-box-footer text-success">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1">
                        <i class="fas fa-user-times"></i>
                    </span>
                    <div class="info-box-content">
                        <span class="info-box-text">Inactive Staff</span>
                        <span class="info-box-number">{{ inactive_staff }}</span>
                        <a href="{% url 'manage_staff' %}?is_active=False" class="small-box-footer text-warning">
                            View Details <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main row -->
        <div class="row">
            <!-- Quick Actions Card -->
            <div class="col-md-6">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt mr-2"></i>Quick Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <a href="{% url 'add_staff' %}" class="btn btn-primary btn-block">
                                    <i class="fas fa-user-plus mr-2"></i>Add Staff
                                </a>
                            </div>
                            <div class="col-6 mb-3">
                                <a href="{% url 'add_accountant' %}" class="btn btn-info btn-block">
                                    <i class="fas fa-user-tie mr-2"></i>Add Accountant
                                </a>
                            </div>
                            <div class="col-6 mb-3">
                                <a href="{% url 'department' %}" class="btn btn-success btn-block">
                                    <i class="fas fa-building mr-2"></i>Departments
                                </a>
                            </div>
                            <div class="col-6 mb-3">
                                <a href="{% url 'list_payslip' %}" class="btn btn-warning btn-block">
                                    <i class="fas fa-file-invoice-dollar mr-2"></i>Payslips
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities Card -->
            <div class="col-md-6">
                <div class="card card-success">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history mr-2"></i>Recent Activities
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if recent_activities %}
                        <div class="timeline">
                            {% for activity in recent_activities %}
                            <div class="time-label">
                                <span class="bg-green">{{ activity.date|default:"Today" }}</span>
                            </div>
                            <div>
                                <i class="fas fa-user bg-blue"></i>
                                <div class="timeline-item">
                                    <h3 class="timeline-header">{{ activity }}</h3>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent activities</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status Row -->
        <div class="row">
            <div class="col-md-12">
                <div class="card card-info">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie mr-2"></i>System Overview
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box bg-gradient-info">
                                    <span class="info-box-icon"><i class="fas fa-building"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Departments</span>
                                        <span class="info-box-number">{{ total_departments|default:"0" }}</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">Active Departments</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box bg-gradient-success">
                                    <span class="info-box-icon"><i class="fas fa-id-badge"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Designations</span>
                                        <span class="info-box-number">{{ total_designations|default:"0" }}</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">Available Positions</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box bg-gradient-warning">
                                    <span class="info-box-icon"><i class="fas fa-file-invoice"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">This Month</span>
                                        <span class="info-box-number">{{ monthly_payslips|default:"0" }}</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 70%"></div>
                                        </div>
                                        <span class="progress-description">Payslips Generated</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 col-12">
                                <div class="info-box bg-gradient-danger">
                                    <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">System</span>
                                        <span class="info-box-number">Online</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="width: 100%"></div>
                                        </div>
                                        <span class="progress-description">Status: Active</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock content %}

