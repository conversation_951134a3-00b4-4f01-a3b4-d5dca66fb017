{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>P.M.S | Log In</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="{{ csrf_token }}">
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{% static 'img/ntc.png' %}">
  <link rel="stylesheet" href="{% static 'plugins/fontawesome-free/css/all.min.css' %}">
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <link rel="stylesheet" href="{% static 'plugins/icheck-bootstrap/icheck-bootstrap.min.css' %}">
  <link rel="stylesheet" href="{% static 'dist/css/adminlte.min.css' %}">

  <style>
    .login-page {
      background-image: url("{% static 'img/1.jpg' %}");
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }
  </style>
  
  <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>

<body class="hold-transition login-page">  
  <div class="login-box">
    
    <div class="card card-outline card-primary">
      <div class="card-header text-center">
        <h4><b>Payroll</b> Management System</h4>
      </div>
      <div class="card-body login-card-body">
        <p class="login-box-msg">Sign in to start your session</p>
        
        {% if messages %}
          {% for message in messages %}
            {% if message.tags == 'error' %}
              <div class="alert alert-danger text-center">
                {{ message }}
              </div>
            {% endif %}
          {% endfor %}
        {% endif %}

        <form action="doLogin/" method="post">
          {% csrf_token %}
          
          <div class="input-group mb-3">
            {{ form.email }}  
            <div class="input-group-append">
              <div class="input-group-text">
                <span class="fas fa-envelope"></span>
              </div>
            </div>
          </div>

          <div class="input-group mb-3">
            {{ form.password }} 
            <div class="input-group-append">
              <div class="input-group-text">
                <span class="fas fa-lock"></span>
              </div>
            </div>
          </div>

          <!-- <div class="input-group mb-3">
            <div class="g-recaptcha" data-sitekey="{{ GOOGLE_RECAPTCHA_SITE_KEY }}"></div>
          </div> -->
          
          <div class="row">
            <div class="col-8">
              <div class="icheck-primary">
                {{ form.remember_me }}  
                <label for="id_remember_me">
                  Remember Me
                </label>
              </div>
            </div>
            <div class="col-4">
              <button type="submit" class="btn btn-primary btn-block">Log In</button>
            </div>
          </div>
        </form>

      </div> 
    </div>
  </div>

  <script src="{% static 'plugins/jquery/jquery.min.js' %}"></script>
  <script src="{% static 'plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'dist/js/adminlte.min.js' %}"></script>
</body>
</html>
