{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'plugins/bs-stepper/css/bs-stepper.min.css' %}">
{% endblock extra_css %}

{% block content %}
<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <div class="card card-primary">
          <div class="card-header">
            <h3 class="card-title">{{page_title}}</h3>
          </div>
          <div class="card-body p-0">
            <div class="bs-stepper">
              <div class="bs-stepper-header" role="tablist">
                <div class="step" data-target="#month-year-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="month-year-part"
                    id="month-year-part-trigger">
                    <span class="bs-stepper-circle">1</span>
                    <span class="bs-stepper-label">Month & Year</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#deductions-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="deductions-part"
                    id="deductions-part-trigger">
                    <span class="bs-stepper-circle">2</span>
                    <span class="bs-stepper-label">Deductions</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#paid-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="paid-part"
                    id="paid-part-trigger">
                    <span class="bs-stepper-circle">3</span>
                    <span class="bs-stepper-label">Pad Values</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#attendence-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="attendence-part"
                    id="attendence-part-trigger">
                    <span class="bs-stepper-circle">4</span>
                    <span class="bs-stepper-label">Attendence</span>
                  </button>
                </div>

                <div class="line"></div>
                <div class="step" data-target="#generate-part">
                  <button type="button" class="step-trigger" role="tab" aria-controls="generate-part"
                    id="generate-part-trigger">
                    <span class="bs-stepper-circle">5</span>
                    <span class="bs-stepper-label">Generate</span>
                  </button>
                </div>
              </div>

              <div id="month-year-part" class="content active bs-stepper-content" role="tabpanel"
                aria-labelledby="month-year-part-trigger">
                <div class="row">
                  <div class="col-md-8">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Enter Details</h3>
                      </div>
                      <div class="card-body">
                        <form id="calendar-form">
                          {% csrf_token %}
                          <div class="form-group">
                            <div class="alert alert-warning">
                              Important: Edit if there are updates. If not, submit and proceed.
                            </div>
                          </div>
                          <div class="form-group">
                            <label for="month">Month & Year</label>
                            <div class="input-group date" id="monthpicker" data-target-input="nearest">
                              <input type="month" class="form-control datetimepicker-input" data-target="#monthpicker"
                                name="month" id="month" />
                              <div class="input-group-append" data-target="#monthpicker" data-toggle="datetimepicker">
                                <div class="input-group-text"><i class="fa fa-calendar mr-2"></i></div>
                              </div>
                            </div>
                          </div>
                          <div class="form-group">
                            {% if latest_entry %}
                            <table class="table table-bordered">
                              <thead>
                                <tr>
                                  <th>Division</th>
                                  <th>Month</th>
                                  <th>DA</th>
                                  <th>HRA</th>
                                  <th>Action</th>
                                </tr>
                              </thead>
                              <tbody>
                                {% for allowance in latest_entry %}
                                <tr>
                                  <td>
                                    <input type="text" class="form-control" name="division"
                                      value="{{ allowance.division }}" readonly>
                                  </td>
                                  <td>
                                    <div class="input-group date" id="monthpicker_{{ forloop.counter }}"
                                      data-target-input="nearest">
                                      <input type="month" class="form-control datetimepicker-input"
                                        data-target="#monthpicke_{{ forloop.counter }}r" name="month"
                                        id="month_{{ forloop.counter }}" />
                                      <div class="input-group-append" data-target="#monthpicker_{{ forloop.counter }}"
                                        data-toggle="datetimepicker">
                                        <div class="input-group-text"><i class="fa fa-calendar mr-2"></i></div>
                                      </div>
                                    </div>
                                  </td>
                                  <td>
                                    <input type="text" class="form-control" name="da" value="{{ allowance.da }}"
                                      readonly>
                                  </td>
                                  <td>
                                    <input type="text" class="form-control" name="hra" value="{{ allowance.hra }}"
                                      readonly>
                                  </td>
                                  <td>
                                    <input type="hidden" name="fixed_id" value="{{ allowance.id }}">
                                    <button type="button" class="btn btn-success save-btn-fixed">
                                      <i class="fas fa-save mr-2"></i>Save
                                    </button>
                                  </td>
                                </tr>
                                {% endfor %}
                              </tbody>
                            </table>
                            {% else %}
                            <p>No fixed allowances found.</p>
                            {% endif %}
                          </div>
                        </form>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="card bg-gradient-warning">
                      <div class="card-header border-0">
                        <h3 class="card-title">
                          <i class="far fa-calendar-alt mr-2"></i>
                          Calendar
                        </h3>
                        <div class="card-tools">
                          <button type="button" class="btn btn-warning btn-sm" data-card-widget="collapse">
                            <i class="fas fa-minus mr-2"></i>
                          </button>
                          <button type="button" class="btn btn-warning btn-sm" data-card-widget="remove">
                            <i class="fas fa-times mr-2"></i>
                          </button>
                        </div>
                      </div>
                      <div class="card-body pt-0">
                        <div id="calendar" style="width: 100%; height: 300px;"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <button class="btn btn-primary" onclick="stepper.next()">Next</button>
              </div>

              <div id="deductions-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="deductions-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Enter Deduction Value</h3>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <table id="example1" class="table table-bordered table-striped">
                          <thead class="thead-dark">
                            <tr>
                              <th>EmpCode</th>
                              <th>Name</th>
                              <th>Department</th>
                              <th>Designation</th>
                              <th>Society (₹)</th>
                              <th>Income Tax (₹)</th>
                              <th>Canteen (₹)</th>
                              <th>Advance (₹)</th>
                              <th>Insurance (₹)</th>
                              <th>Miscellaneous (₹)</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form" method="post" action="{% url 'save_deduction_details' %}">
                                {% csrf_token %}
                                <td>{{ staff.emp_code}}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td><input type="text" name="society" id="society_id"
                                    value="{{staff.deductions.society}}" class="form-control" required></td>
                                <td><input type="text" name="income_tax" id="income_tax_id"
                                    value="{{staff.deductions.income_tax}}" class="form-control" required>
                                </td>
                                <td><input type="text" name="canteen" id="canteen_id"
                                    value="{{staff.deductions.canteen}}" class="form-control" required></td>
                                <td><input type="text" name="advance" id="advance_id"
                                    value="{{staff.deductions.advance}}" class="form-control" required></td>
                                <td><input type="text" name="insurance" id="insurance_id"
                                    value="{{staff.deductions.insurance}}" class="form-control" required>
                                </td>
                                <td><input type="text" name="other" id="other_id" value="{{staff.deductions.other}}"
                                    class="form-control" required>
                                </td>

                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-deduction"><i
                                      class="fas fa-save mr-2"></i>Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                  </div>
                  <!-- /.col -->
                </div>
                <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                <button class="btn btn-primary" onclick="stepper.next()">Next</button>
              </div>

              <div id="paid-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="paid-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Enter Pay Values</h3>
                      </div>
                      <div class="card-body">
                        <table id="example2" class="table table-bordered table-striped">
                          <thead class="thead-dark">
                            <tr>
                              <th>EmpCode</th>
                              <th>Name</th>
                              <th>Department </th>
                              <th>Designation </th>
                              <th>Arrears (₹)</th>
                              <th>Advance (₹)</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form">
                                {% csrf_token %}
                                <td>{{ staff.emp_code }}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td><input type="text" name="arrears" id="arrears_id"
                                    value="{{staff.contract_pay.arrears}}" class="form-control" required></td>
                                <td><input type="text" name="other" id="other_id" value="{{staff.contract_pay.other}}"
                                    class="form-control" required></td>
                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-pay"><i
                                      class="fas fa-save mr-2"></i> Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>

                    </div>
                  </div>
                </div>
                <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                <button class="btn btn-primary" onclick="stepper.next()">Next</button>
              </div>

              <div id="attendence-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="attendence-part-trigger">
                <div class="row">
                  <div class="col-md-12">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Enter Attendance</h3>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <table id="example3" class="table table-bordered table-striped">
                          <thead class="thead-dark">
                            <tr>
                              <th>EmpCode</th>
                              <th>Name</th>
                              <th>Department</th>
                              <th>Designation</th>
                              <th>Paid Days</th>
                              <th>Paid Leave</th>
                              <th>Action</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for staff in staffs %}
                            <tr>
                              <form class="staff-form" method="post" action="{% url 'save_attendence_details' %}">
                                {% csrf_token %}
                                <td>{{ staff.emp_code }}</td>
                                <td>{{ staff.user.first_name }} {{ staff.user.last_name }}</td>
                                <td>{{ staff.department.name }}</td>
                                <td>{{ staff.designation.name }}</td>
                                <td>
                                  <input type="text" name="paid_days" id="paid_days_id_{{ forloop.counter }}"
                                    value="{{ staff.attendance.paid_days }}" class="form-control" required>
                                </td>
                                <td>
                                  <input type="text" name="lop" id="lop_id_{{ forloop.counter }}"
                                    value="{{ staff.attendance.lop }}" class="form-control" required>
                                </td>
                                <td>
                                  <input type="hidden" name="staff" id="staff_id" value="{{staff.id}}">
                                  <button type="button" class="btn btn-success save-btn-attendence"><i
                                      class="fas fa-save mr-2"></i>Save</button>
                                </td>
                              </form>
                            </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->
                  </div>
                  <!-- /.col -->
                </div>
                <!-- /.row -->
                <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
                <button class="btn btn-primary" onclick="stepper.next()">Next</button>
              </div>

              <div id="generate-part" class="content bs-stepper-content" role="tabpanel"
                aria-labelledby="generate-part-trigger">
                <div class="row">
                  <div class="col-md-3"></div>
                  <div class="col-md-6">
                    <div class="card card-primary card-outline">
                      <div class="card-header">
                        <h3 class="card-title">Medical Details</h3>
                      </div>
                      <!-- /.card-header -->
                      <div class="card-body">
                        <form id="medical-form">
                          <div class="form-group">
                            <div class="alert alert-warning">
                              Important: Medical is given every 3 months.
                            </div>
                          </div>
                          <div class="form-group">
                            <label for="give_medical">Give Medical This Month:</label>
                            <select id="give_medical" name="give_medical" class="form-control">
                              <option value="no">No</option>
                              <option value="yes">Yes</option>
                            </select>
                          </div>
                          <button type="button" id="calculate-payroll-btn" class="btn btn-success">
                            <i class="fas fa-calculator mr-2"></i> Calculate Payroll
                          </button>
                        </form>
                      </div>
                      <!-- /.card-body -->
                    </div>
                    <!-- /.card -->

                  </div>
                  <div class="col-md-3"></div>
                </div>
                <button class="btn btn-primary" onclick="stepper.previous()">Previous</button>
              </div>

            </div>
          </div>
          <!-- /.bs-stepper-content -->
        </div>
        <!-- /.bs-stepper -->
      </div>
      <!-- /.card-body -->
    </div>
    <!-- /.card -->
  </div>
  <!-- /.col -->
  </div>
  <!-- /.row -->
  </div>
  <!-- /.container-fluid -->
</section>
<!-- /.content -->
{% endblock content %}

{% block extra_js %}
<!-- BS-Stepper -->
<script src="{% static 'plugins/bs-stepper/js/bs-stepper.min.js' %}"></script>
<script src="{% static 'plugins/moment/moment.min.js' %}"></script>
<script src="{% static 'plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js' %}"></script>

<script>

  document.addEventListener('DOMContentLoaded', function () {
    window.stepper = new Stepper(document.querySelector('.bs-stepper'));
  });

  document.addEventListener('DOMContentLoaded', function () {
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() === 0 ? 12 : today.getMonth()).toString().padStart(2, '0');
    const inputs = document.querySelectorAll('input[type="month"]'); // Select all month inputs
    inputs.forEach(input => {
      const id = input.id; // Get the ID of the current input
      if (id) {
        const inputYear = today.getMonth() === 0 ? year - 1 : year;
        input.value = `${inputYear}-${month}`;
      }
    });
  });

  function getDaysInPreviousMonth() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth();
    const lastDayOfPreviousMonth = new Date(year, month, 0).getDate();
    return lastDayOfPreviousMonth;
  }

  const previousMonthDays = getDaysInPreviousMonth();

  document.querySelectorAll('input[name="paid_days"]').forEach(input => {
    if (!input.value) {
      input.value = previousMonthDays;
    }
  });

  document.querySelectorAll('input[name="lop"],input[name="arrears"],input[name="other"]').forEach(input => {
    if (!input.value) {
      input.value = 0;
    }
  });

  function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === (name + '=')) {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  const csrftoken = getCookie('csrftoken');

  $.ajaxSetup({
    beforeSend: function (xhr, settings) {
      if (!/^(GET|HEAD|OPTIONS|TRACE)$/.test(settings.type) && !this.crossDomain) {
        xhr.setRequestHeader("X-CSRFToken", csrftoken);
      }
    }
  });


  $(function () {
    $('#monthpicker').datetimepicker({
      format: 'YYYY-MM',
      viewMode: 'months'
    });
  });

  $(document).ready(function () {
    $('#calendar-form').on("click", ".save-btn-fixed", function () {
      let row = $(this).closest('tr');
      let formData = row.find('input,select').serializeArray();
      let thisButton = $(this);

      // Add CSRF token manually
      formData.push({name: 'csrfmiddlewaretoken', value: csrftoken});

      $.ajax({
        type: 'POST',
        url: '{% url "save_fixed" %}',
        data: $.param(formData),
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message); // Use Toastr for success
          } else {
            if (typeof response.errors === 'object') { // Handle validation errors
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else { // Handle general error message
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          if (xhr.responseJSON && xhr.responseJSON.errors) {
            for (const field in xhr.responseJSON.errors) {
              toastr.error(xhr.responseJSON.errors[field]);
            }
          } else {
            toastr.error("An unexpected error occurred. Please try again.");
          }
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example1").on("click", ".save-btn-deduction", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_deduction_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });

  $(document).ready(function () {
    $("#example2").on("click", ".save-btn-pay", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_regular_pay" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });


  $(document).ready(function () {
    $("#example3").on("click", ".save-btn-attendence", function () {
      let form = $(this).closest('tr').find('form');
      let formData = form.serialize();
      let thisButton = $(this);

      $.ajax({
        type: 'POST',
        url: '{% url "save_attendence_details" %}',
        data: formData,
        dataType: 'json',
        beforeSend: function () {
          thisButton.attr("disabled", true); // Disable the button
        },
        success: function (response) {
          if (response.success) {
            toastr.success(response.message);
          } else {
            if (typeof response.errors === 'object') {
              for (const field in response.errors) {
                toastr.error(response.errors[field]);
              }
            } else {
              toastr.error(response.errors);
            }
          }
        },
        error: function (xhr, status, error) {
          toastr.error("An unexpected error occurred.");
        },
        complete: function () {
          thisButton.attr("disabled", false); // Enable the button after the request is complete
        }
      });
    });
  });


  document.getElementById('calculate-payroll-btn').addEventListener('click', function () {
    const giveMedical = document.getElementById('give_medical').value;
    const url = `{% url 'edit_payroll_regular' %}?give_medical=${giveMedical}`;
    window.location.href = url;


  });

  $(document).ready(function () {
    $('#calendar').datetimepicker({
      format: 'L',
      inline: true
    });
  });

</script>
{% endblock extra_js %}