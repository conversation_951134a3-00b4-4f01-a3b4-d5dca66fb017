import logging

# Get an instance of a logger
logger = logging.getLogger(__name__)
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect, render
from datetime import datetime, timedelta
from .forms import *
from .models import *

def staff_home(request):
    logger.info("Entered staff_home view")
    try:
        staff = get_object_or_404(Staff, user=request.user)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=40)
        recent_payslips = Payslip.objects.filter(
            staff=staff,
            month__gte=start_date,
            month__lte=end_date
        ).order_by('-month')
        context = {
            'page_title': 'Staff Dashboard',
            'recent_payslips': recent_payslips,
            'start_date': start_date.strftime('%B %Y'),
            'end_date': end_date.strftime('%B %Y'),
        }
        logger.info("Rendering staff_home template for staff %s", staff.user.email)
        return render(request, 'staff_template/home_content.html', context)
    except Exception as e:
        logger.error("Error in staff_home: %s", str(e), exc_info=True)
        messages.error(request, "Error loading dashboard.")
        return redirect('staff_view_profile')

def staff_view_profile(request):
    logger.info("Entered staff_view_profile view")
    try:
        staff = get_object_or_404(Staff, user=request.user)
        context = {
            'page_title': 'Profile',
            'staff': staff,
        }
        logger.info("Rendering staff_view_profile template for staff %s", staff.user.email)
        return render(request, "staff_template/staff_view_profile.html", context)
    except Exception as e:
        logger.error("Error in staff_view_profile: %s", str(e), exc_info=True)
        messages.error(request, "Error loading profile.")
        return redirect('staff_home')

def staff_list_payslips(request):
    logger.info("Entered staff_list_payslips view")
    try:
        staff = get_object_or_404(Staff, user=request.user)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=180)
        recent_payslips = Payslip.objects.filter(
            staff=staff,
            month__gte=start_date,
            month__lte=end_date
        ).order_by('-month')
        context = {
            'page_title': 'List of Payslips',
            'payslips': recent_payslips,
        }
        logger.info("Rendering staff_list_payslips template for staff %s", staff.user.email)
        return render(request, "staff_template/staff_list_payslips.html", context)
    except Exception as e:
        logger.error("Error in staff_list_payslips: %s", str(e), exc_info=True)
        messages.error(request, "Error loading payslips.")
        return redirect('staff_home')

@login_required
def staff_change_password(request):
    logger.info("Entered staff_change_password view")
    try:
        if request.method == 'POST':
            form = StaffPasswordChangeForm(request.user, request.POST)
            if form.is_valid():
                new_password = form.cleaned_data['new_password1']
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, 'Password changed successfully. Please log in again.')
                logger.info("Password changed for user %s", request.user.email)
                return redirect('user_logout')
            else:
                for error in form.errors.values():
                    messages.error(request, error)
                logger.warning("Invalid password change attempt for user %s", request.user.email)
                return redirect('staff_view_profile')
        else:
            return redirect('staff_view_profile')
    except Exception as e:
        logger.error("Error in staff_change_password: %s", str(e), exc_info=True)
        messages.error(request, "Error changing password.")
        return redirect('staff_view_profile')

@login_required
def staff_leave_list(request):
    logger.info("Entered staff_leave_list view")
    try:
        staff = get_object_or_404(Staff, user=request.user)
        leave_records = LeaveRecord.objects.filter(staff=staff).select_related('leave_type').order_by('-start_date')
        context = {
            'leave_records': leave_records,
            'page_title': 'My Leave Records',
        }
        logger.info("Rendering staff_leave_list template for staff %s", staff.user.email)
        return render(request, 'staff_template/leave_list.html', context)
    except Exception as e:
        logger.error("Error in staff_leave_list: %s", str(e), exc_info=True)
        messages.error(request, "Error loading leave records.")
        return redirect('staff_home')
