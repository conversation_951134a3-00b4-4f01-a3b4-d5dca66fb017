{% extends 'main_app/base.html' %}
{% load static %}
{% block page_title %}{{page_title}}{% endblock page_title %}

{% block content %}

<!-- Main content -->
<section class="content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-md-6">
        <!-- Profile Image -->
        <div class="card card-primary card-outline">
          <div class="card-body box-profile">
            <div class="text-center">
              {% if user.profile_pic %}
              <img class="profile-user-img img-fluid img-circle" src="{{ user.profile_pic}}" alt="User profile picture">
              {% else %}
              <img class="profile-user-img img-fluid img-circle" src="{% static 'dist/img/default-avatar.jpeg' %}"
                alt="User profile picture">
              {% endif %}
            </div>

            <h3 class="profile-username text-center">{{ user.first_name }} {{ user.last_name }}</h3>
            <p class="text-muted text-center">Staff</p>
            <ul class="list-group list-group-unbordered mb-3">
              <li class="list-group-item">
                <b>Email</b> <a class="float-right">{{ staff.user.email }}</a>
              </li>
              <li class="list-group-item">
                <b>Father's Name</b> <a class="float-right">{{ staff.user.father_name }}</a>
              </li>
              <li class="list-group-item">
                <b>Gender</b> <a class="float-right">{{ staff.user.gender }}</a>
              </li>
              <li class="list-group-item">
                <b>Date of Joning</b> <a class="float-right">{{ staff.emp_doj }}</a>
              </li>
              <li class="list-group-item">
                <b>Empolye Code</b> <a class="float-right">{{ staff.emp_code }}</a>
              </li>
              <li class="list-group-item">
                <b>UNA Code</b> <a class="float-right">{{ staff.uan }}</a>
              </li>
              <!-- <li class="list-group-item">
                <b>Division</b> <a class="float-right">{{ staff.division }}</a>
              </li> -->
              <li class="list-group-item">
                <b>Department</b> <a class="float-right">{{ staff.department }}</a>
              </li>
              <li class="list-group-item">
                <b>Designations</b> <a class="float-right">{{ staff.designation }}</a>
              </li>
              <!-- <li class="list-group-item">
                <b>Basic Salary</b> <a class="float-right">{{ staff.basic_amt }}</a>
              </li> -->

            </ul>
            <div class="text-center mt-3">
              <button class="btn btn-warning" data-toggle="modal" data-target="#changePasswordModal">
                <i class="fas fa-key"></i> Change Password
              </button>
            </div>
          </div>
          <!-- /.card-body -->
        </div>
        <!-- /.card -->
      </div>
      <div class="col-md-3"></div>
      <!-- /.col -->
    </div>
    <!-- /.row -->
  </div><!-- /.container-fluid -->
</section>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" role="dialog" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="changePasswordForm" method="post" action="{% url 'change_password' %}">
        {% csrf_token %}
        <div class="modal-body">
          <div class="form-group">
            <label for="old_password">Old Password</label>
            <input type="password" class="form-control" id="old_password" name="old_password" required>
          </div>
          <div class="form-group">
            <label for="new_password1">New Password</label>
            <input type="password" class="form-control" id="new_password1" name="new_password1" required>
          </div>
          <div class="form-group">
            <label for="new_password2">Confirm New Password</label>
            <input type="password" class="form-control" id="new_password2" name="new_password2" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Change Password</button>
        </div>
      </form>
    </div>
  </div>
</div>
<!-- /.content -->
{% endblock content %}

{% block custom_js %}
<script>
  $(document).ready(function () {
    var ifNotfied = false;
    $("#id_password").on("change", function () {
      if (!ifNotfied) {
        ifNotfied = true;
        alert("After a successful profile update:\n\nYour session would be terminated\nYou would be required to login again")
      }
    })

    // Change Password form validation
    $('#changePasswordForm').on('submit', function(e) {
      var newPassword = $('#new_password1').val();
      var confirmPassword = $('#new_password2').val();
      if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match!');
      }
    });
  })
</script>
{% endblock custom_js %}