from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

class DeveloperConsoleMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
            try:
                if "text/html" in response.get("Content-Type", "") and b"</body>" in response.content:
                    script = b"""
                        <script>
                            (function() {
                                let encoded = "Y29uc29sZS5sb2coIiVjIERldmVsb3AgYW5kIERlc2lnbiBieSBBcG9vcnYgRGVlcCBTYWh1IC0gaHR0cHM6Ly93d3cubGlua2VkaW4uY29tL2luL2Fwb29ydi1kZWVwLyIsICJjb2xvcjogIzRDQ0I1MDsgZm9udC1zaXplOiA5cHg7IGZvbnQtd2VpZ2h0OiBib2xkOyIpOw==";
                                eval(atob(encoded));
                            })();
                        </script>
                    """
                    response.content = response.content.replace(b"</body>", script + b"</body>")
            except Exception:
                pass
            return response
